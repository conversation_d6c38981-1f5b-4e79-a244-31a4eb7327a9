/**
 * 地图视图组件
 * 提供地图显示和交互功能
 */
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Card, Select, Button, Space, InputNumber, message, Tooltip } from 'antd';
import { 
  EnvironmentOutlined, 
  ZoomInOutlined, 
  ZoomOutOutlined,
  AimOutlined,
  SettingOutlined,
  FullscreenOutlined
} from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import './MapView.scss';

const { Option } = Select;

/**
 * 地理坐标接口
 */
export interface GeographicCoordinate {
  longitude: number;
  latitude: number;
  altitude?: number;
}

/**
 * 地图类型
 */
export type MapType = 'osm' | 'satellite' | 'terrain';

/**
 * 地图视图属性接口
 */
export interface MapViewProps {
  center?: GeographicCoordinate;
  zoom?: number;
  mapType?: MapType;
  width?: number | string;
  height?: number | string;
  showControls?: boolean;
  showCoordinates?: boolean;
  onLocationChange?: (location: GeographicCoordinate) => void;
  onZoomChange?: (zoom: number) => void;
  onMapTypeChange?: (mapType: MapType) => void;
  className?: string;
}

/**
 * 地图视图组件
 */
export const MapView: React.FC<MapViewProps> = ({
  center = { longitude: 116.404, latitude: 39.915 }, // 默认北京坐标
  zoom = 10,
  mapType = 'osm',
  width = '100%',
  height = 400,
  showControls = true,
  showCoordinates = true,
  onLocationChange,
  onZoomChange,
  onMapTypeChange,
  className
}) => {
  const { t } = useTranslation();
  const mapRef = useRef<HTMLDivElement>(null);
  const [mapInstance, setMapInstance] = useState<any>(null);
  const [currentLocation, setCurrentLocation] = useState(center);
  const [currentZoom, setCurrentZoom] = useState(zoom);
  const [currentMapType, setCurrentMapType] = useState(mapType);
  const [isLoading, setIsLoading] = useState(true);
  const [mouseCoordinates, setMouseCoordinates] = useState<GeographicCoordinate | null>(null);

  /**
   * 初始化地图
   */
  const initializeMap = useCallback(async () => {
    if (!mapRef.current) return;

    try {
      setIsLoading(true);
      
      // 这里应该集成实际的地图库（如Leaflet、OpenLayers等）
      // 目前使用模拟实现
      const mockMap = {
        center: currentLocation,
        zoom: currentZoom,
        mapType: currentMapType,
        
        setView: (center: GeographicCoordinate, zoom: number) => {
          mockMap.center = center;
          mockMap.zoom = zoom;
          setCurrentLocation(center);
          setCurrentZoom(zoom);
          onLocationChange?.(center);
          onZoomChange?.(zoom);
        },
        
        setMapType: (type: MapType) => {
          mockMap.mapType = type;
          setCurrentMapType(type);
          onMapTypeChange?.(type);
        },
        
        zoomIn: () => {
          const newZoom = Math.min(mockMap.zoom + 1, 18);
          mockMap.setView(mockMap.center, newZoom);
        },
        
        zoomOut: () => {
          const newZoom = Math.max(mockMap.zoom - 1, 1);
          mockMap.setView(mockMap.center, newZoom);
        },
        
        panTo: (center: GeographicCoordinate) => {
          mockMap.setView(center, mockMap.zoom);
        }
      };
      
      setMapInstance(mockMap);
      
      // 模拟地图加载
      setTimeout(() => {
        setIsLoading(false);
      }, 1000);
      
    } catch (error) {
      console.error('地图初始化失败:', error);
      message.error('地图初始化失败');
      setIsLoading(false);
    }
  }, [currentLocation, currentZoom, currentMapType, onLocationChange, onZoomChange, onMapTypeChange]);

  /**
   * 处理地图类型变化
   */
  const handleMapTypeChange = (type: string) => {
    if (mapInstance) {
      mapInstance.setMapType(type);
    }
  };

  /**
   * 处理缩放变化
   */
  const handleZoomChange = (newZoom: number | null) => {
    if (newZoom && mapInstance) {
      mapInstance.setView(currentLocation, newZoom);
    }
  };

  /**
   * 处理中心点变化
   */
  const handleCenterChange = (field: 'longitude' | 'latitude', value: number | null) => {
    if (value !== null && mapInstance) {
      const newCenter = {
        ...currentLocation,
        [field]: value
      };
      mapInstance.setView(newCenter, currentZoom);
    }
  };

  /**
   * 缩放到当前位置
   */
  const zoomToCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newCenter = {
            longitude: position.coords.longitude,
            latitude: position.coords.latitude
          };
          
          if (mapInstance) {
            mapInstance.setView(newCenter, 15);
          }
        },
        (error) => {
          console.error('获取位置失败:', error);
          message.error('获取当前位置失败');
        }
      );
    } else {
      message.error('浏览器不支持地理定位');
    }
  };

  /**
   * 处理鼠标移动
   */
  const handleMouseMove = (event: React.MouseEvent) => {
    if (!mapRef.current) return;
    
    const rect = mapRef.current.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    
    // 简单的坐标转换（实际应该使用地图库的转换方法）
    const longitude = currentLocation.longitude + (x - rect.width / 2) * 0.001;
    const latitude = currentLocation.latitude - (y - rect.height / 2) * 0.001;
    
    setMouseCoordinates({ longitude, latitude });
  };

  /**
   * 处理地图点击
   */
  const handleMapClick = (_event: React.MouseEvent) => {
    if (mouseCoordinates && mapInstance) {
      mapInstance.setView(mouseCoordinates, currentZoom);
    }
  };

  // 初始化地图
  useEffect(() => {
    initializeMap();
  }, [initializeMap]);

  // 监听外部属性变化
  useEffect(() => {
    if (mapInstance && (center.longitude !== currentLocation.longitude || center.latitude !== currentLocation.latitude)) {
      mapInstance.setView(center, currentZoom);
    }
  }, [center, mapInstance, currentLocation, currentZoom]);

  useEffect(() => {
    if (mapInstance && zoom !== currentZoom) {
      mapInstance.setView(currentLocation, zoom);
    }
  }, [zoom, mapInstance, currentLocation, currentZoom]);

  return (
    <Card 
      className={`map-view ${className || ''}`}
      title={
        <Space>
          <EnvironmentOutlined />
          {t('spatial.mapView.title', '地图视图')}
        </Space>
      }
      extra={
        showControls && (
          <Space>
            <Select
              value={currentMapType}
              onChange={handleMapTypeChange}
              style={{ width: 120 }}
            >
              <Option value="osm">街道地图</Option>
              <Option value="satellite">卫星地图</Option>
              <Option value="terrain">地形地图</Option>
            </Select>
            
            <Tooltip title="定位到当前位置">
              <Button 
                icon={<AimOutlined />} 
                onClick={zoomToCurrentLocation}
              />
            </Tooltip>
            
            <Tooltip title="全屏显示">
              <Button icon={<FullscreenOutlined />} />
            </Tooltip>
          </Space>
        )
      }
      style={{ width, height: typeof height === 'number' ? height + 100 : height }}
    >
      <div className="map-container">
        {/* 地图控制面板 */}
        {showControls && (
          <div className="map-controls">
            <Space direction="vertical" size="small">
              <div className="coordinate-inputs">
                <Space>
                  <span>经度:</span>
                  <InputNumber
                    value={currentLocation.longitude}
                    onChange={(value) => handleCenterChange('longitude', value)}
                    precision={6}
                    step={0.001}
                    style={{ width: 120 }}
                  />
                </Space>
                <Space>
                  <span>纬度:</span>
                  <InputNumber
                    value={currentLocation.latitude}
                    onChange={(value) => handleCenterChange('latitude', value)}
                    precision={6}
                    step={0.001}
                    style={{ width: 120 }}
                  />
                </Space>
                <Space>
                  <span>缩放:</span>
                  <InputNumber
                    value={currentZoom}
                    onChange={handleZoomChange}
                    min={1}
                    max={18}
                    style={{ width: 80 }}
                  />
                </Space>
              </div>
              
              <div className="zoom-controls">
                <Space>
                  <Button 
                    icon={<ZoomInOutlined />} 
                    onClick={() => mapInstance?.zoomIn()}
                    disabled={currentZoom >= 18}
                  >
                    放大
                  </Button>
                  <Button 
                    icon={<ZoomOutOutlined />} 
                    onClick={() => mapInstance?.zoomOut()}
                    disabled={currentZoom <= 1}
                  >
                    缩小
                  </Button>
                </Space>
              </div>
            </Space>
          </div>
        )}
        
        {/* 地图显示区域 */}
        <div 
          ref={mapRef}
          className={`map-display ${isLoading ? 'loading' : ''}`}
          style={{ 
            width: '100%', 
            height: typeof height === 'number' ? height - 50 : 350,
            backgroundColor: '#f0f0f0',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            position: 'relative',
            cursor: 'crosshair'
          }}
          onMouseMove={handleMouseMove}
          onClick={handleMapClick}
        >
          {isLoading ? (
            <div className="map-loading">
              <div className="loading-spinner" />
              <div>加载地图中...</div>
            </div>
          ) : (
            <div className="map-content">
              {/* 地图中心点标记 */}
              <div className="center-marker">
                <EnvironmentOutlined style={{ fontSize: 24, color: '#ff4d4f' }} />
              </div>
              
              {/* 地图信息显示 */}
              <div className="map-info">
                <div>中心: {currentLocation.longitude.toFixed(6)}, {currentLocation.latitude.toFixed(6)}</div>
                <div>缩放: {currentZoom}</div>
                <div>类型: {currentMapType}</div>
              </div>
            </div>
          )}
        </div>
        
        {/* 坐标显示 */}
        {showCoordinates && mouseCoordinates && (
          <div className="coordinates-display">
            鼠标位置: {mouseCoordinates.longitude.toFixed(6)}, {mouseCoordinates.latitude.toFixed(6)}
          </div>
        )}
      </div>
    </Card>
  );
};
